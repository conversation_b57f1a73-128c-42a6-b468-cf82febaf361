﻿using Mapster;
using ModelContextProtocol.Server;
using RealPlusMCP.Api.Models;
using RealPlusNLP.Elasticsearch.Contracts;
using RealPlusNLP.Elasticsearch.Messages;
using System.ComponentModel;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace RealPlusMCP.Api.Tools;

[McpServerToolType,
    Description("Convert a free-form property search query into filters, run it against the listing index, and return matching listings.")]
public sealed class ListingSearchTool
{
    private static readonly string[] PublicSecureGroups =
        ["ACRIS", "NEST", "ONEKEY", "PUBLIC", "RLS", "RPDATADEPT", "STREETEASY"];

    [McpServerTool(Name = "get_listings"),
         Description("Convert a free-form property search query into filters, run it against the listing index, and return matching listings. Return a structured search result. Input = searchText(natural-language query). Output = { listings[], unprocessedCriteria }.")]
    public async Task<SearchResultModel<ListingModel>> GetListings(
        IElasticSearchService elasticSearchService,
        [Description("Natural-language query, e.g. ‘1-3 bed, ≥2 bath condo in Midtown ≤1 000 sq ft’. The tool translates this to structured filters.")]
        string searchText)
    {
        var nlpResult = await GetNlpResponse(searchText);
        var viewOptions = nlpResult!.SearchOptions;
        viewOptions.SecureGroups = PublicSecureGroups;
        var esOptions = viewOptions.Adapt<ElasticSearchOptions>();
        esOptions.SecureGroup = "CORC";

        var result = await elasticSearchService.GetListingSearchResultsAsync(esOptions);

        return new SearchResultModel<ListingModel>(
            result.ListingsData.Adapt<List<ListingModel>>(),
            nlpResult.UnprocessedCriteria);
    }

    private static async Task<QueryToSearchOptionsApiResponse?> GetNlpResponse(string text)
    {
        var content = new { query = text };

        var client = new HttpClient();
        var request = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri("https://ca-nlp-api-dev.orangefield-593414ae.eastus2.azurecontainerapps.io/listings/query-to-search-options"),
            Headers =
            {
                { "X-Api-Key", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlMzYwZjIwZS1mZjYwLTQwZjYtYjIwZi1mZjYwZjYwZjYwZjAiLCJpYXQiOjE1NzYwNjYwNzcsImp0aSI6IjIwMjAtMDItMjJUMDk6MzA6MjcuNjYwNjA2WiIsImV4cCI6MTU3NjA2NjA3OH0.1" },
            },
            Content = new StringContent(JsonSerializer.Serialize(content), Encoding.UTF8, "application/json")
            {
                Headers =
                {
                    ContentType = new MediaTypeHeaderValue("application/json")
                }
            }
        };

        using var response = await client.SendAsync(request);
        response.EnsureSuccessStatusCode();

        return await response.Content.ReadFromJsonAsync<QueryToSearchOptionsApiResponse>();
    }

    record QueryToSearchOptionsApiResponse(
        SearchCriteriaViewModel SearchOptions,
        string? UnprocessedCriteria);
}

public sealed record SearchResultModel<TModel>(
    IReadOnlyCollection<TModel> Listings,
    [property: Description("Any words the parser could not map to a filter (may be null or empty).")]
    string? UnmatchedText);